import { EventEmitter } from 'events';
import dgram from 'dgram';
import os from 'os';
import { logger } from '../utils/logger';

interface NetworkNode {
  id: string;
  name: string;
  ip: string;
  port: number;
  role: 'master' | 'slave';
  lastSeen: Date;
  version: string;
  capabilities: string[];
}

interface DiscoveryMessage {
  type: 'announce' | 'discover' | 'response';
  nodeId: string;
  nodeName: string;
  ip: string;
  port: number;
  role: 'master' | 'slave';
  version: string;
  capabilities: string[];
  timestamp: number;
}

export class NetworkDiscoveryService extends EventEmitter {
  private socket: dgram.Socket;
  private nodes: Map<string, NetworkNode> = new Map();
  private nodeId: string;
  private nodeName: string;
  private role: 'master' | 'slave' = 'slave';
  private port: number;
  private discoveryPort: number = 45678;
  private announceInterval: NodeJS.Timeout | null = null;
  private cleanupInterval: NodeJS.Timeout | null = null;
  private isRunning: boolean = false;

  constructor(nodeId: string, nodeName: string, port: number) {
    super();
    this.nodeId = nodeId;
    this.nodeName = nodeName;
    this.port = port;
    this.socket = dgram.createSocket('udp4');
    this.setupSocket();
  }

  private setupSocket() {
    this.socket.on('message', this.handleMessage.bind(this));
    this.socket.on('error', (err) => {
      logger.error('Network discovery socket error:', err);
      this.emit('error', err);
    });
  }

  private handleMessage(msg: Buffer, rinfo: dgram.RemoteInfo) {
    try {
      const message: DiscoveryMessage = JSON.parse(msg.toString());
      
      // Ignore messages from self
      if (message.nodeId === this.nodeId) return;

      const node: NetworkNode = {
        id: message.nodeId,
        name: message.nodeName,
        ip: message.ip,
        port: message.port,
        role: message.role,
        lastSeen: new Date(),
        version: message.version,
        capabilities: message.capabilities
      };

      const wasNew = !this.nodes.has(node.id);
      this.nodes.set(node.id, node);

      if (wasNew) {
        logger.info(`Discovered new CBT node: ${node.name} (${node.ip}:${node.port})`);
        this.emit('nodeDiscovered', node);
      } else {
        this.emit('nodeUpdated', node);
      }

      // Respond to discovery requests
      if (message.type === 'discover') {
        this.sendAnnouncement('response', rinfo.address);
      }

    } catch (error) {
      logger.error('Error parsing discovery message:', error);
    }
  }

  private sendAnnouncement(type: 'announce' | 'discover' | 'response', targetIp?: string) {
    const message: DiscoveryMessage = {
      type,
      nodeId: this.nodeId,
      nodeName: this.nodeName,
      ip: this.getLocalIP(),
      port: this.port,
      role: this.role,
      version: process.env.npm_package_version || '1.0.0',
      capabilities: ['sync', 'realtime', 'offline'],
      timestamp: Date.now()
    };

    const buffer = Buffer.from(JSON.stringify(message));
    const target = targetIp || '***************';

    this.socket.send(buffer, this.discoveryPort, target, (err) => {
      if (err) {
        logger.error('Error sending discovery message:', err);
      }
    });
  }

  private getLocalIP(): string {
    const interfaces = os.networkInterfaces();
    for (const name of Object.keys(interfaces)) {
      const iface = interfaces[name];
      if (iface) {
        for (const alias of iface) {
          if (alias.family === 'IPv4' && !alias.internal) {
            return alias.address;
          }
        }
      }
    }
    return '127.0.0.1';
  }

  private cleanupStaleNodes() {
    const now = new Date();
    const staleThreshold = 30000; // 30 seconds

    for (const [nodeId, node] of this.nodes.entries()) {
      if (now.getTime() - node.lastSeen.getTime() > staleThreshold) {
        this.nodes.delete(nodeId);
        logger.info(`Removed stale node: ${node.name}`);
        this.emit('nodeRemoved', node);
      }
    }
  }

  public start(): void {
    if (this.isRunning) return;

    this.socket.bind(this.discoveryPort, () => {
      this.socket.setBroadcast(true);
      logger.info(`Network discovery started on port ${this.discoveryPort}`);
      
      // Send initial discovery request
      this.sendAnnouncement('discover');
      
      // Start periodic announcements
      this.announceInterval = setInterval(() => {
        this.sendAnnouncement('announce');
      }, 10000); // Every 10 seconds
      
      // Start cleanup of stale nodes
      this.cleanupInterval = setInterval(() => {
        this.cleanupStaleNodes();
      }, 15000); // Every 15 seconds
      
      this.isRunning = true;
      this.emit('started');
    });
  }

  public stop(): void {
    if (!this.isRunning) return;

    if (this.announceInterval) {
      clearInterval(this.announceInterval);
      this.announceInterval = null;
    }

    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }

    this.socket.close();
    this.isRunning = false;
    this.nodes.clear();
    
    logger.info('Network discovery stopped');
    this.emit('stopped');
  }

  public setRole(role: 'master' | 'slave'): void {
    this.role = role;
    if (this.isRunning) {
      this.sendAnnouncement('announce');
    }
  }

  public getNodes(): NetworkNode[] {
    return Array.from(this.nodes.values());
  }

  public getMasterNode(): NetworkNode | null {
    for (const node of this.nodes.values()) {
      if (node.role === 'master') {
        return node;
      }
    }
    return null;
  }

  public getSlaveNodes(): NetworkNode[] {
    return Array.from(this.nodes.values()).filter(node => node.role === 'slave');
  }

  public isRunning(): boolean {
    return this.isRunning;
  }
}
