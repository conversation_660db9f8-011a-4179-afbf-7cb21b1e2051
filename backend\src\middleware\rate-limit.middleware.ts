import rateLimit from 'express-rate-limit';
import { Request, Response } from 'express';

// Create a rate limiter factory function
export const rateLimitMiddleware = (windowMs: number, max: number) => {
  return rateLimit({
    windowMs: windowMs * 1000, // Convert seconds to milliseconds
    max, // Limit each IP to max requests per windowMs
    message: {
      success: false,
      message: 'Too many requests from this IP, please try again later.',
      retryAfter: Math.ceil(windowMs / 1000)
    },
    standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
    legacyHeaders: false, // Disable the `X-RateLimit-*` headers
    handler: (req: Request, res: Response) => {
      res.status(429).json({
        success: false,
        message: 'Rate limit exceeded',
        retryAfter: Math.ceil(windowMs / 1000),
        limit: max,
        windowMs
      });
    },
    skip: (req: Request) => {
      // Skip rate limiting for localhost in development
      if (process.env.NODE_ENV === 'development' && req.ip === '127.0.0.1') {
        return true;
      }
      return false;
    }
  });
};

// Specific rate limiters for different endpoints
export const authRateLimit = rateLimitMiddleware(900, 5); // 5 attempts per 15 minutes
export const apiRateLimit = rateLimitMiddleware(60, 100); // 100 requests per minute
export const syncRateLimit = rateLimitMiddleware(60, 200); // 200 sync requests per minute
export const uploadRateLimit = rateLimitMiddleware(60, 10); // 10 uploads per minute
