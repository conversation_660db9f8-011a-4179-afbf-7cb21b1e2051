import { Request, Response } from 'express';
import { NetworkDiscoveryService } from '../services/network-discovery.service';
import { DatabaseSyncService } from '../services/database-sync.service';
import { logger } from '../utils/logger';
import { v4 as uuidv4 } from 'uuid';

interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    role: string;
  };
}

export class NetworkSyncController {
  private discoveryService: NetworkDiscoveryService;
  private syncService: DatabaseSyncService;
  private nodeId: string;
  private conflicts: Map<string, any> = new Map();
  private syncStats = {
    totalOperations: 0,
    successfulOperations: 0,
    failedOperations: 0,
    lastSyncTime: null as Date | null,
    averageResponseTime: 0,
    networkErrors: 0
  };

  constructor() {
    this.nodeId = process.env.NODE_ID || uuidv4();
    const nodeName = process.env.NODE_NAME || `CBT-${require('os').hostname()}`;
    const port = parseInt(process.env.PORT || '5000');

    this.discoveryService = new NetworkDiscoveryService(this.nodeId, nodeName, port);
    this.syncService = new DatabaseSyncService(this.discoveryService, this.nodeId);
    
    this.setupEventHandlers();
    this.startServices();
  }

  private setupEventHandlers() {
    this.syncService.on('syncConflicts', (conflicts) => {
      conflicts.forEach((conflict: any) => {
        const conflictId = uuidv4();
        this.conflicts.set(conflictId, { ...conflict, id: conflictId, timestamp: new Date() });
      });
    });

    this.syncService.on('operationQueued', () => {
      this.syncStats.totalOperations++;
    });

    this.syncService.on('syncError', () => {
      this.syncStats.failedOperations++;
      this.syncStats.networkErrors++;
    });
  }

  private async startServices() {
    try {
      this.discoveryService.start();
      await this.syncService.startSync();
      logger.info('Network sync services started successfully');
    } catch (error) {
      logger.error('Failed to start network sync services:', error);
    }
  }

  public getDiscoveredNodes = async (req: Request, res: Response): Promise<any> => {
    try {
      const nodes = this.discoveryService.getNodes();
      const currentNode = {
        id: this.nodeId,
        name: process.env.NODE_NAME || `CBT-${require('os').hostname()}`,
        ip: this.getLocalIP(),
        port: parseInt(process.env.PORT || '5000'),
        role: this.discoveryService.getMasterNode()?.id === this.nodeId ? 'master' : 'slave',
        isCurrent: true
      };

      return res.status(200).json({
        success: true,
        message: 'Discovered nodes retrieved successfully',
        data: {
          currentNode,
          discoveredNodes: nodes,
          totalNodes: nodes.length + 1
        }
      });
    } catch (error: any) {
      logger.error('Error getting discovered nodes:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to get discovered nodes',
        error: error.message
      });
    }
  };

  public setNodeRole = async (req: Request, res: Response): Promise<any> => {
    try {
      const { role } = req.body;
      
      this.syncService.setMasterMode(role === 'master');
      
      return res.status(200).json({
        success: true,
        message: `Node role set to ${role}`,
        data: { role, nodeId: this.nodeId }
      });
    } catch (error: any) {
      logger.error('Error setting node role:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to set node role',
        error: error.message
      });
    }
  };

  public getFullSyncData = async (req: Request, res: Response): Promise<any> => {
    try {
      const { lastSync } = req.query;
      const lastSyncDate = lastSync ? new Date(lastSync as string) : new Date(0);
      
      // This would typically fetch all data modified since lastSync
      // For now, return empty array as placeholder
      const syncData: any[] = [];
      
      return res.status(200).json({
        success: true,
        message: 'Full sync data retrieved',
        data: syncData,
        metadata: {
          lastSync: lastSyncDate,
          currentTime: new Date(),
          operationCount: syncData.length
        }
      });
    } catch (error: any) {
      logger.error('Error getting full sync data:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to get full sync data',
        error: error.message
      });
    }
  };

  public getSyncUpdates = async (req: Request, res: Response): Promise<any> => {
    try {
      const { lastSync } = req.query;
      const lastSyncDate = lastSync ? new Date(lastSync as string) : new Date(0);
      
      // This would typically fetch incremental updates
      const updates: any[] = [];
      
      return res.status(200).json({
        success: true,
        message: 'Sync updates retrieved',
        data: updates,
        metadata: {
          lastSync: lastSyncDate,
          currentTime: new Date(),
          updateCount: updates.length
        }
      });
    } catch (error: any) {
      logger.error('Error getting sync updates:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to get sync updates',
        error: error.message
      });
    }
  };

  public handleSlaveOperations = async (req: Request, res: Response): Promise<any> => {
    try {
      const { operations, slaveNodeId, timestamp } = req.body;
      
      // Process operations from slave node
      for (const operation of operations) {
        this.syncService.queueOperation(operation.table, operation.operation, operation.data);
      }
      
      this.syncStats.successfulOperations += operations.length;
      this.syncStats.lastSyncTime = new Date();
      
      return res.status(200).json({
        success: true,
        message: 'Slave operations processed successfully',
        data: {
          processedOperations: operations.length,
          slaveNodeId,
          timestamp
        }
      });
    } catch (error: any) {
      logger.error('Error handling slave operations:', error);
      this.syncStats.failedOperations += req.body.operations?.length || 0;
      return res.status(500).json({
        success: false,
        message: 'Failed to process slave operations',
        error: error.message
      });
    }
  };

  public handleMasterOperations = async (req: Request, res: Response): Promise<any> => {
    try {
      const { operations, masterNodeId, timestamp } = req.body;
      
      // Apply operations from master node
      for (const operation of operations) {
        this.syncService.queueOperation(operation.table, operation.operation, operation.data);
      }
      
      this.syncStats.successfulOperations += operations.length;
      this.syncStats.lastSyncTime = new Date();
      
      return res.status(200).json({
        success: true,
        message: 'Master operations applied successfully',
        data: {
          appliedOperations: operations.length,
          masterNodeId,
          timestamp
        }
      });
    } catch (error: any) {
      logger.error('Error handling master operations:', error);
      this.syncStats.failedOperations += req.body.operations?.length || 0;
      return res.status(500).json({
        success: false,
        message: 'Failed to apply master operations',
        error: error.message
      });
    }
  };

  public getNetworkStatus = async (req: Request, res: Response): Promise<any> => {
    try {
      const nodes = this.discoveryService.getNodes();
      const masterNode = this.discoveryService.getMasterNode();
      const isOnline = this.syncService.isOnlineMode();
      const queueLength = this.syncService.getQueueLength();
      
      return res.status(200).json({
        success: true,
        message: 'Network status retrieved',
        data: {
          nodeId: this.nodeId,
          isOnline,
          isMaster: masterNode?.id === this.nodeId,
          masterNode,
          connectedNodes: nodes.length,
          queueLength,
          lastSyncTime: this.syncStats.lastSyncTime
        }
      });
    } catch (error: any) {
      logger.error('Error getting network status:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to get network status',
        error: error.message
      });
    }
  };

  public getNetworkHealth = async (req: Request, res: Response): Promise<any> => {
    try {
      const nodes = this.discoveryService.getNodes();
      const healthyNodes = nodes.filter(node => 
        new Date().getTime() - node.lastSeen.getTime() < 30000
      );
      
      return res.status(200).json({
        success: true,
        message: 'Network health retrieved',
        data: {
          totalNodes: nodes.length + 1, // +1 for current node
          healthyNodes: healthyNodes.length + 1,
          unhealthyNodes: nodes.length - healthyNodes.length,
          networkHealth: ((healthyNodes.length + 1) / (nodes.length + 1)) * 100,
          lastHealthCheck: new Date()
        }
      });
    } catch (error: any) {
      logger.error('Error getting network health:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to get network health',
        error: error.message
      });
    }
  };

  public getConflicts = async (req: Request, res: Response): Promise<any> => {
    try {
      const conflicts = Array.from(this.conflicts.values());
      
      return res.status(200).json({
        success: true,
        message: 'Conflicts retrieved',
        data: {
          conflicts,
          totalConflicts: conflicts.length
        }
      });
    } catch (error: any) {
      logger.error('Error getting conflicts:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to get conflicts',
        error: error.message
      });
    }
  };

  public resolveConflict = async (req: Request, res: Response): Promise<any> => {
    try {
      const { conflictId, resolution, mergedData } = req.body;
      
      const conflict = this.conflicts.get(conflictId);
      if (!conflict) {
        return res.status(404).json({
          success: false,
          message: 'Conflict not found'
        });
      }
      
      // Apply resolution logic here
      this.conflicts.delete(conflictId);
      
      return res.status(200).json({
        success: true,
        message: 'Conflict resolved successfully',
        data: { conflictId, resolution }
      });
    } catch (error: any) {
      logger.error('Error resolving conflict:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to resolve conflict',
        error: error.message
      });
    }
  };

  private getLocalIP(): string {
    const interfaces = require('os').networkInterfaces();
    for (const name of Object.keys(interfaces)) {
      const iface = interfaces[name];
      if (iface) {
        for (const alias of iface) {
          if (alias.family === 'IPv4' && !alias.internal) {
            return alias.address;
          }
        }
      }
    }
    return '127.0.0.1';
  }

  // Placeholder methods for remaining endpoints
  public triggerFullSync = async (req: Request, res: Response): Promise<any> => {
    return res.status(200).json({ success: true, message: 'Full sync triggered' });
  };

  public triggerIncrementalSync = async (req: Request, res: Response): Promise<any> => {
    return res.status(200).json({ success: true, message: 'Incremental sync triggered' });
  };

  public getNetworkConfig = async (req: Request, res: Response): Promise<any> => {
    return res.status(200).json({ success: true, data: {} });
  };

  public updateNetworkConfig = async (req: Request, res: Response): Promise<any> => {
    return res.status(200).json({ success: true, message: 'Config updated' });
  };

  public enableOfflineMode = async (req: Request, res: Response): Promise<any> => {
    return res.status(200).json({ success: true, message: 'Offline mode enabled' });
  };

  public disableOfflineMode = async (req: Request, res: Response): Promise<any> => {
    return res.status(200).json({ success: true, message: 'Offline mode disabled' });
  };

  public getOfflineQueue = async (req: Request, res: Response): Promise<any> => {
    return res.status(200).json({ success: true, data: [] });
  };

  public clearOfflineQueue = async (req: Request, res: Response): Promise<any> => {
    return res.status(200).json({ success: true, message: 'Queue cleared' });
  };

  public getSyncStats = async (req: Request, res: Response): Promise<any> => {
    return res.status(200).json({ success: true, data: this.syncStats });
  };

  public getSyncLogs = async (req: Request, res: Response): Promise<any> => {
    return res.status(200).json({ success: true, data: [] });
  };

  public getBluetoothDevices = async (req: Request, res: Response): Promise<any> => {
    return res.status(200).json({ success: true, data: [] });
  };

  public pairBluetoothDevice = async (req: Request, res: Response): Promise<any> => {
    return res.status(200).json({ success: true, message: 'Device paired' });
  };

  public syncViaBluetoothDevice = async (req: Request, res: Response): Promise<any> => {
    return res.status(200).json({ success: true, message: 'Bluetooth sync completed' });
  };
}
