import { EventEmitter } from 'events';
import axios from 'axios';
import { prisma } from '../config/database';
import { logger } from '../utils/logger';
import { NetworkDiscoveryService } from './network-discovery.service';

interface SyncOperation {
  id: string;
  table: string;
  operation: 'INSERT' | 'UPDATE' | 'DELETE';
  data: any;
  timestamp: Date;
  nodeId: string;
  version: number;
}

interface SyncConflict {
  operation: SyncOperation;
  conflictType: 'version' | 'concurrent' | 'deleted';
  localData?: any;
  remoteData?: any;
}

export class DatabaseSyncService extends EventEmitter {
  private discoveryService: NetworkDiscoveryService;
  private syncQueue: SyncOperation[] = [];
  private isOnline: boolean = true;
  private syncInterval: NodeJS.Timeout | null = null;
  private lastSyncTimestamp: Date = new Date(0);
  private nodeId: string;
  private isMaster: boolean = false;

  constructor(discoveryService: NetworkDiscoveryService, nodeId: string) {
    super();
    this.discoveryService = discoveryService;
    this.nodeId = nodeId;
    this.setupEventHandlers();
  }

  private setupEventHandlers() {
    this.discoveryService.on('nodeDiscovered', (node) => {
      if (node.role === 'master' && !this.isMaster) {
        this.startSyncWithMaster(node);
      }
    });

    this.discoveryService.on('nodeRemoved', (node) => {
      if (node.role === 'master' && !this.isMaster) {
        this.handleMasterDisconnection();
      }
    });
  }

  public async startSync(): Promise<void> {
    // Start periodic sync
    this.syncInterval = setInterval(() => {
      this.performSync();
    }, 5000); // Sync every 5 seconds

    logger.info('Database sync service started');
    this.emit('syncStarted');
  }

  public stopSync(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }
    logger.info('Database sync service stopped');
    this.emit('syncStopped');
  }

  public setMasterMode(isMaster: boolean): void {
    this.isMaster = isMaster;
    this.discoveryService.setRole(isMaster ? 'master' : 'slave');
    
    if (isMaster) {
      logger.info('Node set as master - will accept sync requests');
    } else {
      logger.info('Node set as slave - will sync with master');
    }
  }

  private async startSyncWithMaster(masterNode: any): Promise<void> {
    try {
      logger.info(`Starting sync with master: ${masterNode.name} (${masterNode.ip}:${masterNode.port})`);
      
      // Perform initial full sync
      await this.performFullSync(masterNode);
      
      this.emit('masterConnected', masterNode);
    } catch (error) {
      logger.error('Failed to sync with master:', error);
      this.emit('syncError', error);
    }
  }

  private async performFullSync(masterNode: any): Promise<void> {
    try {
      const response = await axios.get(`http://${masterNode.ip}:${masterNode.port}/api/sync/full`, {
        timeout: 30000,
        headers: {
          'X-Node-ID': this.nodeId,
          'X-Last-Sync': this.lastSyncTimestamp.toISOString()
        }
      });

      const { data: syncData } = response.data;
      
      if (syncData && syncData.length > 0) {
        await this.applySyncOperations(syncData);
        this.lastSyncTimestamp = new Date();
        logger.info(`Full sync completed: ${syncData.length} operations applied`);
      }

    } catch (error) {
      logger.error('Full sync failed:', error);
      throw error;
    }
  }

  private async performSync(): Promise<void> {
    if (!this.isOnline) return;

    try {
      if (this.isMaster) {
        await this.handleMasterSync();
      } else {
        await this.handleSlaveSync();
      }
    } catch (error) {
      logger.error('Sync operation failed:', error);
      this.emit('syncError', error);
    }
  }

  private async handleMasterSync(): Promise<void> {
    // Master broadcasts changes to all slaves
    const slaves = this.discoveryService.getSlaveNodes();
    
    if (this.syncQueue.length > 0) {
      const operations = [...this.syncQueue];
      this.syncQueue = [];

      for (const slave of slaves) {
        try {
          await axios.post(`http://${slave.ip}:${slave.port}/api/sync/operations`, {
            operations,
            masterNodeId: this.nodeId,
            timestamp: new Date().toISOString()
          }, {
            timeout: 10000,
            headers: { 'X-Node-ID': this.nodeId }
          });
        } catch (error) {
          logger.error(`Failed to sync with slave ${slave.name}:`, error);
          // Re-queue operations for retry
          this.syncQueue.unshift(...operations);
        }
      }
    }
  }

  private async handleSlaveSync(): Promise<void> {
    const master = this.discoveryService.getMasterNode();
    if (!master) return;

    try {
      // Send any local changes to master
      if (this.syncQueue.length > 0) {
        const operations = [...this.syncQueue];
        
        await axios.post(`http://${master.ip}:${master.port}/api/sync/slave-operations`, {
          operations,
          slaveNodeId: this.nodeId,
          timestamp: new Date().toISOString()
        }, {
          timeout: 10000,
          headers: { 'X-Node-ID': this.nodeId }
        });

        this.syncQueue = [];
      }

      // Get updates from master
      const response = await axios.get(`http://${master.ip}:${master.port}/api/sync/updates`, {
        timeout: 10000,
        headers: {
          'X-Node-ID': this.nodeId,
          'X-Last-Sync': this.lastSyncTimestamp.toISOString()
        }
      });

      const { data: updates } = response.data;
      if (updates && updates.length > 0) {
        await this.applySyncOperations(updates);
        this.lastSyncTimestamp = new Date();
      }

    } catch (error) {
      if (error.code === 'ECONNREFUSED' || error.code === 'ETIMEDOUT') {
        this.handleMasterDisconnection();
      } else {
        throw error;
      }
    }
  }

  private async applySyncOperations(operations: SyncOperation[]): Promise<void> {
    const conflicts: SyncConflict[] = [];

    for (const operation of operations) {
      try {
        await this.applySingleOperation(operation);
      } catch (error) {
        if (error.code === 'SYNC_CONFLICT') {
          conflicts.push(error.conflict);
        } else {
          logger.error('Failed to apply sync operation:', error);
        }
      }
    }

    if (conflicts.length > 0) {
      this.emit('syncConflicts', conflicts);
    }
  }

  private async applySingleOperation(operation: SyncOperation): Promise<void> {
    const { table, operation: op, data, timestamp, nodeId } = operation;

    try {
      switch (op) {
        case 'INSERT':
          await this.handleInsert(table, data, timestamp, nodeId);
          break;
        case 'UPDATE':
          await this.handleUpdate(table, data, timestamp, nodeId);
          break;
        case 'DELETE':
          await this.handleDelete(table, data, timestamp, nodeId);
          break;
      }
    } catch (error) {
      logger.error(`Failed to apply ${op} operation on ${table}:`, error);
      throw error;
    }
  }

  private async handleInsert(table: string, data: any, timestamp: Date, nodeId: string): Promise<void> {
    // Add metadata for sync tracking
    const insertData = {
      ...data,
      sync_timestamp: timestamp,
      sync_node_id: nodeId,
      sync_version: 1
    };

    await (prisma as any)[table].create({ data: insertData });
  }

  private async handleUpdate(table: string, data: any, timestamp: Date, nodeId: string): Promise<void> {
    const { id, ...updateData } = data;
    
    // Check for conflicts
    const existing = await (prisma as any)[table].findUnique({ where: { id } });
    if (existing && existing.sync_timestamp > timestamp) {
      const conflict: SyncConflict = {
        operation: { id: '', table, operation: 'UPDATE', data, timestamp, nodeId, version: 1 },
        conflictType: 'version',
        localData: existing,
        remoteData: data
      };
      throw { code: 'SYNC_CONFLICT', conflict };
    }

    await (prisma as any)[table].update({
      where: { id },
      data: {
        ...updateData,
        sync_timestamp: timestamp,
        sync_node_id: nodeId,
        sync_version: existing ? existing.sync_version + 1 : 1
      }
    });
  }

  private async handleDelete(table: string, data: any, timestamp: Date, nodeId: string): Promise<void> {
    const { id } = data;
    
    await (prisma as any)[table].update({
      where: { id },
      data: {
        deleted_at: timestamp,
        sync_timestamp: timestamp,
        sync_node_id: nodeId
      }
    });
  }

  private handleMasterDisconnection(): void {
    this.isOnline = false;
    logger.warn('Master disconnected - entering offline mode');
    this.emit('masterDisconnected');
    
    // Try to reconnect after 10 seconds
    setTimeout(() => {
      this.isOnline = true;
      const master = this.discoveryService.getMasterNode();
      if (master) {
        this.startSyncWithMaster(master);
      }
    }, 10000);
  }

  public queueOperation(table: string, operation: 'INSERT' | 'UPDATE' | 'DELETE', data: any): void {
    const syncOp: SyncOperation = {
      id: `${this.nodeId}-${Date.now()}-${Math.random()}`,
      table,
      operation,
      data,
      timestamp: new Date(),
      nodeId: this.nodeId,
      version: 1
    };

    this.syncQueue.push(syncOp);
    this.emit('operationQueued', syncOp);
  }

  public getQueueLength(): number {
    return this.syncQueue.length;
  }

  public isOnlineMode(): boolean {
    return this.isOnline;
  }
}
