import { Router } from 'express';
import { body, query, validationResult } from 'express-validator';
import { NetworkSyncController } from '../controllers/network-sync.controller';
import { authenticateToken } from '../middleware/auth.middleware';
import { rateLimitMiddleware } from '../middleware/rate-limit.middleware';

const router = Router();
const networkSyncController = new NetworkSyncController();

// Validation middleware
const validateSyncRequest = [
  body('operations').isArray().withMessage('Operations must be an array'),
  body('operations.*.table').isString().withMessage('Table name is required'),
  body('operations.*.operation').isIn(['INSERT', 'UPDATE', 'DELETE']).withMessage('Invalid operation'),
  body('operations.*.data').isObject().withMessage('Data must be an object'),
  body('masterNodeId').optional().isString(),
  body('slaveNodeId').optional().isString(),
  body('timestamp').isISO8601().withMessage('Valid timestamp required'),
  (req: any, res: any, next: any) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }
    next();
  }
];

const validateNodeRequest = [
  query('lastSync').optional().isISO8601().withMessage('Invalid lastSync timestamp'),
  (req: any, res: any, next: any) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }
    next();
  }
];

// Network discovery endpoints
router.get('/nodes', 
  rateLimitMiddleware(60, 100), // 100 requests per minute
  networkSyncController.getDiscoveredNodes
);

router.post('/nodes/role', 
  rateLimitMiddleware(60, 10), // 10 requests per minute
  body('role').isIn(['master', 'slave']).withMessage('Role must be master or slave'),
  validateSyncRequest[4], // Use the validation result middleware
  networkSyncController.setNodeRole
);

// Sync endpoints for master node
router.get('/full',
  rateLimitMiddleware(60, 5), // 5 full syncs per minute
  validateNodeRequest,
  networkSyncController.getFullSyncData
);

router.get('/updates',
  rateLimitMiddleware(60, 60), // 60 requests per minute
  validateNodeRequest,
  networkSyncController.getSyncUpdates
);

router.post('/slave-operations',
  rateLimitMiddleware(60, 120), // 120 requests per minute
  validateSyncRequest,
  networkSyncController.handleSlaveOperations
);

// Sync endpoints for slave nodes
router.post('/operations',
  rateLimitMiddleware(60, 120), // 120 requests per minute
  validateSyncRequest,
  networkSyncController.handleMasterOperations
);

// Network status endpoints
router.get('/status',
  rateLimitMiddleware(60, 30), // 30 requests per minute
  networkSyncController.getNetworkStatus
);

router.get('/health',
  rateLimitMiddleware(60, 60), // 60 requests per minute
  networkSyncController.getNetworkHealth
);

// Conflict resolution endpoints
router.get('/conflicts',
  rateLimitMiddleware(60, 30), // 30 requests per minute
  networkSyncController.getConflicts
);

router.post('/conflicts/resolve',
  rateLimitMiddleware(60, 20), // 20 requests per minute
  body('conflictId').isString().withMessage('Conflict ID is required'),
  body('resolution').isIn(['local', 'remote', 'merge']).withMessage('Invalid resolution type'),
  body('mergedData').optional().isObject(),
  validateSyncRequest[4], // Use the validation result middleware
  networkSyncController.resolveConflict
);

// Manual sync triggers
router.post('/trigger/full',
  rateLimitMiddleware(60, 3), // 3 full syncs per minute
  networkSyncController.triggerFullSync
);

router.post('/trigger/incremental',
  rateLimitMiddleware(60, 10), // 10 incremental syncs per minute
  networkSyncController.triggerIncrementalSync
);

// Network configuration
router.get('/config',
  rateLimitMiddleware(60, 30), // 30 requests per minute
  networkSyncController.getNetworkConfig
);

router.post('/config',
  rateLimitMiddleware(60, 10), // 10 config updates per minute
  body('discoveryPort').optional().isInt({ min: 1024, max: 65535 }),
  body('syncInterval').optional().isInt({ min: 1000, max: 60000 }),
  body('maxRetries').optional().isInt({ min: 1, max: 10 }),
  body('timeout').optional().isInt({ min: 5000, max: 60000 }),
  validateSyncRequest[4], // Use the validation result middleware
  networkSyncController.updateNetworkConfig
);

// Offline mode management
router.post('/offline/enable',
  rateLimitMiddleware(60, 5), // 5 requests per minute
  networkSyncController.enableOfflineMode
);

router.post('/offline/disable',
  rateLimitMiddleware(60, 5), // 5 requests per minute
  networkSyncController.disableOfflineMode
);

router.get('/offline/queue',
  rateLimitMiddleware(60, 30), // 30 requests per minute
  networkSyncController.getOfflineQueue
);

router.post('/offline/queue/clear',
  rateLimitMiddleware(60, 5), // 5 requests per minute
  networkSyncController.clearOfflineQueue
);

// Statistics and monitoring
router.get('/stats',
  rateLimitMiddleware(60, 60), // 60 requests per minute
  networkSyncController.getSyncStats
);

router.get('/logs',
  rateLimitMiddleware(60, 20), // 20 requests per minute
  query('limit').optional().isInt({ min: 1, max: 1000 }),
  query('offset').optional().isInt({ min: 0 }),
  query('level').optional().isIn(['error', 'warn', 'info', 'debug']),
  validateNodeRequest[1], // Use the validation result middleware
  networkSyncController.getSyncLogs
);

// Bluetooth support endpoints (for future implementation)
router.get('/bluetooth/devices',
  rateLimitMiddleware(60, 30), // 30 requests per minute
  networkSyncController.getBluetoothDevices
);

router.post('/bluetooth/pair',
  rateLimitMiddleware(60, 10), // 10 pairing attempts per minute
  body('deviceId').isString().withMessage('Device ID is required'),
  validateSyncRequest[4], // Use the validation result middleware
  networkSyncController.pairBluetoothDevice
);

router.post('/bluetooth/sync',
  rateLimitMiddleware(60, 20), // 20 bluetooth syncs per minute
  body('deviceId').isString().withMessage('Device ID is required'),
  validateSyncRequest[4], // Use the validation result middleware
  networkSyncController.syncViaBluetoothDevice
);

export { router as networkSyncRoutes };
